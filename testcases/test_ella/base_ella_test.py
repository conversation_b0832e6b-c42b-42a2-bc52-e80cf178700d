"""
Ella测试基类
提供通用的测试方法和fixture，简化测试用例编写
"""
import pytest
import allure
import time
from pages.apps.ella.main_page_refactored import EllaMainPageRefactored
from pages.apps.ella.ella_contact_command_handler import EllaContactCommandHandler
from core.logger import log


class BaseEllaTest:
    """Ella测试基类"""
    
    @pytest.fixture(scope="function")
    def ella_app(self):
        """简化的Ella应用fixture"""
        ella_page = EllaMainPageRefactored()
        
        try:
            # 启动应用
            assert ella_page.start_app(), "Ella应用启动失败"
            assert ella_page.wait_for_page_load(timeout=15), "Ella页面加载失败"
            
            log.info("✅ Ella应用启动成功")
            yield ella_page
            
        except Exception as e:
            log.error(f"❌ Ella应用启动异常: {e}")
            pytest.fail(f"Ella应用启动异常: {e}")
        finally:
            # 清理
            try:
                ella_page.stop_app()
            except Exception as e:
                log.warning(f"⚠️ 停止应用异常: {e}")
    
    def execute_command_and_verify(self, ella_app, command: str, expected_status_change: bool = True):
        """
        执行命令并验证结果的通用方法
        
        Args:
            ella_app: Ella应用实例
            command: 要执行的命令
            expected_status_change: 是否期望状态发生变化
            
        Returns:
            tuple: (initial_status, final_status, response_text)
        """
        # 记录初始状态
        initial_status = self._get_initial_status(ella_app, command)
        
        # 确保页面就绪
        self._ensure_page_ready(ella_app)
        
        # 执行命令
        self._execute_command(ella_app, command)
        
        # 等待并获取响应
        response_text = self._wait_and_get_response(ella_app)
        
        # 验证最终状态
        final_status = self._get_final_status(ella_app, command)
        
        # 验证状态变化
        if expected_status_change:
            self._verify_status_change(initial_status, final_status, command)
        
        return initial_status, final_status, response_text
    
    def _get_initial_status(self, ella_app, command: str):
        """获取初始状态"""
        if "bluetooth" in command.lower():
            return ella_app.check_bluetooth_status()
        elif "contact" in command.lower() or "contacts" in command.lower():
            return ella_app.check_contacts_app_opened()
        elif "weather" in command.lower():
            return ella_app.check_weather_app_opened()
        elif "camera" in command.lower() or "photo" in command.lower():
            return ella_app.check_camera_app_opened()
        else:
            return None
    
    def _get_final_status(self, ella_app, command: str):
        """获取最终状态"""
        # 等待状态变化
        time.sleep(3)

        if "bluetooth" in command.lower():
            return ella_app.check_bluetooth_status_smart()
        elif "contact" in command.lower() or "contacts" in command.lower():
            return ella_app.check_contacts_app_opened_smart()
        elif "weather" in command.lower():
            return ella_app.check_weather_app_opened()
        elif "camera" in command.lower() or "photo" in command.lower():
            return ella_app.check_camera_app_opened()
        else:
            return None
    
    def _ensure_page_ready(self, ella_app):
        """确保页面就绪"""
        assert ella_app.ensure_on_chat_page(), "无法确保在对话页面"
        assert ella_app.ensure_input_box_ready(), "输入框未就绪"
    
    def _execute_command(self, ella_app, command: str):
        """执行命令"""
        success = ella_app.execute_text_command(command)
        assert success, f"执行命令失败: {command}"
        log.info(f"✅ 成功执行命令: {command}")
    
    def _wait_and_get_response(self, ella_app, timeout: int = 8):
        """等待并获取响应"""
        # 等待AI响应
        response_received = ella_app.wait_for_response(timeout=timeout)

        if not response_received:
            log.warning("⚠️ 响应超时，尝试直接获取")
            time.sleep(3)

        # 确保返回到Ella应用以获取响应文本
        log.info("确保返回到Ella应用以获取响应文本")
        if not ella_app.ensure_on_chat_page():
            log.warning("无法确保在Ella对话页面，尝试返回")
            ella_app.return_to_ella_app()
            time.sleep(2)

        # 获取响应文本
        response_text = ella_app.get_response_text_smart()
        if not response_text:
            response_text = ella_app.get_response_text()

        log.info(f"AI响应: '{response_text}'")
        return response_text
    
    def _verify_status_change(self, initial_status, final_status, command: str):
        """验证状态变化"""
        if "bluetooth" in command.lower():
            if "open" in command.lower():
                assert final_status, f"蓝牙未开启: 初始={initial_status}, 最终={final_status}"
            elif "close" in command.lower():
                assert not final_status, f"蓝牙未关闭: 初始={initial_status}, 最终={final_status}"
        elif ("contact" in command.lower() or "contacts" in command.lower()) and "open" in command.lower():
            assert final_status, f"联系人应用未打开: 初始={initial_status}, 最终={final_status}"

        log.info(f"✅ 状态验证通过: {initial_status} -> {final_status}")
    
    def create_test_summary(self, command: str, initial_status, final_status, response_text: str):
        """创建测试总结"""
        status_change = "是" if initial_status != final_status else "否"
        
        summary = f"""
测试命令: {command}
响应内容: {response_text}
初始状态: {initial_status}
最终状态: {final_status}
状态变化: {status_change}
测试结果: 成功
"""
        return summary.strip()
    
    def attach_test_summary(self, summary: str):
        """附加测试总结到Allure报告"""
        allure.attach(summary, name="测试总结", attachment_type=allure.attachment_type.TEXT)
    
    def take_screenshot(self, ella_app, name: str):
        """截图并附加到Allure报告"""
        screenshot_path = ella_app.screenshot(f"{name}.png")
        allure.attach.file(screenshot_path, name=name, attachment_type=allure.attachment_type.PNG)
        return screenshot_path

    def verify_expected_in_response(self, expected_text, response_text: str):
        """
        验证期望内容是否在响应中

        Args:
            expected_text: 期望的文本内容，可以是字符串或字符串列表
            response_text: 响应文本

        Returns:
            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含
        """
        log.info(f"响应: {response_text}")

        # 如果expected_text是字符串，转换为列表处理
        if isinstance(expected_text, str):
            expected_list = [expected_text]
        elif isinstance(expected_text, list):
            expected_list = expected_text
        else:
            log.error(f"❌ expected_text类型错误: {type(expected_text)}")
            return False

        # 记录所有验证结果
        all_found = True
        found_items = []
        missing_items = []

        # 遍历所有期望内容
        for expected_item in expected_list:
            if expected_item in response_text:
                found_items.append(expected_item)
                log.info(f"✅ 响应包含期望内容: '{expected_item}'")
            else:
                missing_items.append(expected_item)
                log.warning(f"⚠️ 响应未包含期望内容: '{expected_item}'")
                all_found = False

        # 输出总结
        if all_found:
            log.info(f"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})")
        else:
            log.warning(f"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})")
            log.warning(f"缺失内容: {missing_items}")

        return all_found


class SimpleEllaTest(BaseEllaTest):
    """极简版Ella测试基类"""
    
    def simple_command_test(self, ella_app, command: str, verify_status: bool = True):
        """
        极简命令测试方法
        
        Args:
            ella_app: Ella应用实例
            command: 要执行的命令
            verify_status: 是否验证状态变化
        """
        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.execute_command_and_verify(
                ella_app, command, verify_status
            )
        
        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
        
        log.info(f"🎉 {command} 测试完成")
        return initial_status, final_status, response_text

    def execute_contact_command_test(self, ella_app, command: str):
        """
        执行联系人命令测试的专用方法

        Args:
            ella_app: Ella应用实例
            command: 要执行的命令

        Returns:
            tuple: (initial_status, final_status, response_text)
        """
        # 创建联系人命令处理器
        contact_handler = EllaContactCommandHandler(ella_app)

        with allure.step(f"执行联系人命令: {command}"):
            initial_status, final_status, response_text = contact_handler.execute_contact_command_with_retry(command)

        with allure.step("验证联系人命令结果"):
            verified_status = contact_handler.verify_contact_command_result(initial_status, final_status, response_text)
            if verified_status and not final_status:
                # 如果验证通过但原始检测失败，更新最终状态
                final_status = True

        return initial_status, final_status, response_text


# 装饰器简化测试编写
def ella_command_test(command: str, verify_status: bool = True):
    """
    Ella命令测试装饰器
    
    Args:
        command: 要测试的命令
        verify_status: 是否验证状态变化
    """
    def decorator(func):
        def wrapper(self, ella_app):
            return self.simple_command_test(ella_app, command, verify_status)
        return wrapper
    return decorator
