"""
Ella语音助手蓝牙命令测试 - 简洁版本
使用基类和装饰器简化测试编写，提供更清晰的测试结构
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest, ella_command_test
from core.logger import log


@allure.feature("Ella语音助手")
@allure.story("蓝牙控制命令 - 简洁版本")
class TestEllaBluetoothCommandConcise(SimpleEllaTest):
    """Ella蓝牙命令测试类 - 简洁版本"""
    
    @allure.title("测试open bluetooth命令 - 简洁版本")
    @allure.description("使用简化的测试框架测试蓝牙开启命令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_bluetooth_concise(self, ella_app):
        """测试open bluetooth命令 - 简洁版本"""
        self.simple_command_test(ella_app, "open bluetooth")


