2025-07-21 15:15:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 15:15:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:35 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:16:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 15:16:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:20 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:19:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:58 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 15:19:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:20:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 15:20:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:20:06 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:20:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:20:06 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:20:08 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 19:44:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 19:44:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:33 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 19:44:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_text_views:118 | 从TextView获取响应失败: {'code': -32002, 'data': "Selector [className='android.widget.TextView', instance=13]", 'method': 'wait'}
2025-07-21 20:18:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:06 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:18:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:18:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:14 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:15 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:16 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:18:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:18:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:56 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:56 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:59 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:19:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:48 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:19:48 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:48 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:19:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:53 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:19:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:54 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:19:56 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:22:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:23:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:23:03 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-21 20:23:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:23:06 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:23:08 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:28:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:38 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:28:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:38 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:28:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:28:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:43 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:28:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:44 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:28:48 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:30:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:20 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:30:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:20 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:30:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:28 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:30:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:28 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:30:30 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
