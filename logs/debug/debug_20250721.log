2025-07-21 15:15:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:33 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:34 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 15:15:35 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:15:35 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:16:16 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:18 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 15:16:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:16:20 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:19:57 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:58 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 15:19:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:19:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:20:00 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 15:20:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:20:06 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:20:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 15:20:06 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 15:20:08 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 19:44:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:30 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:31 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 19:44:32 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 19:44:33 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 19:44:46 | DEBUG | pages.apps.ella.ella_response_handler:_get_response_from_text_views:118 | 从TextView获取响应失败: {'code': -32002, 'data': "Selector [className='android.widget.TextView', instance=13]", 'method': 'wait'}
2025-07-21 20:18:05 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:06 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:18:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:08 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:18:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:14 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:14 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:15 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:16 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:18:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:51 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:52 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:18:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:56 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:18:56 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:18:59 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:19:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:48 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:19:48 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:48 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:49 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:50 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:19:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:53 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:19:54 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:19:54 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:19:56 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:22:56 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:58 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:22:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:23:03 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:23:03 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [文本输入框(备选)]: False
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音输入按钮]: False
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [语音按钮(备选)]: False
2025-07-21 20:23:04 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: False
2025-07-21 20:23:06 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:23:06 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:23:08 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:28:37 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:38 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:28:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:38 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:28:38 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:39 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:28:43 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:43 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:28:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:28:44 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:28:48 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:30:19 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:20 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:30:20 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:20 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:21 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:30:27 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:28 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:30:28 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:30:28 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:30:30 | DEBUG | pages.apps.ella.ella_app_detector:check_contacts_app_opened:150 | 未找到包名: com.sh.smart.caller
2025-07-21 20:55:22 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:24 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:55:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:24 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:25 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-21 20:55:26 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-21 20:55:26 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-21 20:55:28 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_page_dump:148 | 页面所有文本: <?xml version='1.0' encoding='UTF-8' standalone='yes' ?>

<hierarchy rotation="0">

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="com.transsion.smartpanel:id/floating_view" class="android.widget.RelativeLayout" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1047,297][1080,657]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.smartpanel:id/img_floating_view" class="android.widget.ImageView" package="com.transsion.smartpanel" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[1059,345][1080,609]" drawing-order="1" hint="" display-id="0" />

      </node>

    </node>

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="com.android.systemui:id/status_bar_launch_animation_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0" />

    <node index="1" text="" resource-id="com.android.systemui:id/status_bar_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="2" hint="" display-id="0">

      <node index="0" text="" resource-id="com.android.systemui:id/status_bar" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,123]" drawing-order="1" hint="" display-id="0">

        <node index="0" text="" resource-id="com.android.systemui:id/status_bar_contents" class="android.widget.RelativeLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[36,21][1044,123]" drawing-order="2" hint="" display-id="0">

          <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][503,123]" drawing-order="1" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_content" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/status_bar_start_side_except_heads_up" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][350,123]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="20:55" resource-id="com.android.systemui:id/clock" class="android.widget.TextView" package="com.android.systemui" content-desc="20:55" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[45,21][157,123]" drawing-order="2" hint="" display-id="0" />

                <node index="1" text="" resource-id="com.android.systemui:id/notification_icon_area_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="4" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/notification_icon_area" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="2" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.android.systemui:id/notificationIcons" class="android.view.ViewGroup" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[157,21][350,123]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="时钟通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[212,53][249,90]" drawing-order="2" hint="" display-id="0" />

                      <node index="2" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 系统通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[258,53][295,90]" drawing-order="3" hint="" display-id="0" />

                      <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="DebugLoggerUI通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[304,53][341,90]" drawing-order="4" hint="" display-id="0" />

                    </node>

                  </node>

                </node>

              </node>

            </node>

          </node>

          <node index="1" text="" resource-id="com.android.systemui:id/cutout_space_view" class="android.view.View" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[503,21][576,123]" drawing-order="2" hint="" display-id="0" />

          <node index="2" text="" resource-id="com.android.systemui:id/status_bar_end_side_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[576,21][1035,123]" drawing-order="3" hint="" display-id="0">

            <node index="0" text="" resource-id="com.android.systemui:id/status_bar_end_side_content" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,21][1035,123]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.android.systemui:id/system_icons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[744,39][1035,105]" drawing-order="2" hint="" display-id="0">

                <node index="0" text="" resource-id="com.android.systemui:id/status_icons_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][953,105]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.android.systemui:id/statusIcons" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,39][938,105]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="闹钟已设置为：周六09:00。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[752,41][784,102]" drawing-order="11" hint="" display-id="0" />

                    <node index="3" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="蓝牙开启。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[799,41][822,102]" drawing-order="13" hint="" display-id="0" />

                    <node index="4" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="振铃器静音。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[837,41][867,102]" drawing-order="16" hint="" display-id="0" />

                    <node index="5" text="" resource-id="com.android.systemui:id/mobile_combo" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="手机信号强度为四格。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="21" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.android.systemui:id/mobile_group" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,41][938,102]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.android.systemui:id/sim_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="4" hint="" display-id="0">

                          <node index="0" text="" resource-id="com.android.systemui:id/mobile_type_container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][938,93]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.android.systemui:id/mobile_in" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[882,49][893,93]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.android.systemui:id/mobile_signal" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[896,49][938,93]" drawing-order="2" hint="" display-id="0" />

                            </node>

                            <node index="1" text="" resource-id="com.android.systemui:id/mobile_type" class="android.widget.ImageView" package="com.android.systemui" content-desc="5G" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,49][938,93]" drawing-order="1" hint="" display-id="0" />

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                  <node index="1" text="" resource-id="com.android.systemui:id/airplane_container" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[773,39][938,105]" drawing-order="2" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.android.systemui:id/battery" class="android.widget.LinearLayout" package="com.android.systemui" content-desc="电池电量为百分之 100。" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,39][1020,105]" drawing-order="2" hint="" display-id="0">

                  <node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[953,55][1020,89]" drawing-order="1" hint="" display-id="0" />

                </node>

              </node>

            </node>

          </node>

        </node>

      </node>

    </node>

    <node index="2" text="" resource-id="com.android.systemui:id/container" class="android.widget.FrameLayout" package="com.android.systemui" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,121]" drawing-order="3" hint="" display-id="0" />

  </node>

  <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="0" hint="" display-id="0">

    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

      <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

          <node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="2" hint="" display-id="0">

            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_root" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="1" hint="" display-id="0">

              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/container" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][1080,2400]" drawing-order="3" hint="" display-id="0">

                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_top_tab_layout" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,123][1080,291]" drawing-order="1" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_tab" class="android.widget.HorizontalScrollView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="3" hint="" display-id="0">

                    <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[42,123][620,291]" drawing-order="1" hint="" display-id="0">

                      <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[42,123][385,291]" drawing-order="1" hint="" display-id="0">

                        <node index="0" text="Dialogue" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" visible-to-user="true" bounds="[72,158][355,255]" drawing-order="3" hint="" display-id="0" />

                      </node>

                      <node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[385,123][620,291]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Explore" resource-id="com.transsion.aivoiceassistant:id/tabText" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[415,176][590,237]" drawing-order="3" hint="" display-id="0" />

                      </node>

                    </node>

                  </node>

                  <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_user" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[948,171][1020,243]" drawing-order="1" hint="" display-id="0" />

                </node>

                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/help_main_viewpager" class="androidx.viewpager.widget.ViewPager" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="3" hint="" display-id="0">

                  <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/relative_root" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,2400]" drawing-order="1" hint="" display-id="0">

                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/occupying_view" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,291][1080,294]" drawing-order="1" hint="" display-id="0" />

                    <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_tip_pull_to_refresh" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,312][1080,384]" drawing-order="2" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_left" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,345][205,350]" drawing-order="1" hint="" display-id="0" />

                      <node index="1" text="" resource-id="" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,312][875,384]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="Swipe down to view earlier chats" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[205,325][803,371]" drawing-order="1" hint="" display-id="0" />

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[803,312][875,384]" drawing-order="2" hint="" display-id="0" />

                      </node>

                      <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/tv_pull_tips_view_right" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[875,345][1032,350]" drawing-order="3" hint="" display-id="0" />

                    </node>

                    <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_rv_container" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="3" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/refreshLayout" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                        <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/rv_dialogue" class="androidx.recyclerview.widget.RecyclerView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,384][1080,1964]" drawing-order="1" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][1032,860]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,384][1032,860]" drawing-order="1" hint="" display-id="0">

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend1" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,413][984,526]" drawing-order="3" hint="" display-id="0">

                                <node index="0" text="Switch to a male voice" resource-id="com.transsion.aivoiceassistant:id/tv_recommend1" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,443][936,496]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend2" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,556][984,669]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="Ouaddou Praises Mbuthuma's Impact" resource-id="com.transsion.aivoiceassistant:id/tv_recommend2" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,586][936,639]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="4" text="" resource-id="com.transsion.aivoiceassistant:id/fl_recommend3" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,699][984,812]" drawing-order="5" hint="" display-id="0">

                                <node index="0" text="Musk's Tesla Safety Crisis" resource-id="com.transsion.aivoiceassistant:id/tv_recommend3" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[144,729][936,782]" drawing-order="1" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,908][1032,1021]" drawing-order="2" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/fl_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[502,908][1032,1021]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="open bluetooth" resource-id="com.transsion.aivoiceassistant:id/asr_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[550,938][876,991]" drawing-order="1" hint="" display-id="0" />

                              <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/asr_image" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[894,913][1002,1015]" drawing-order="2" hint="" display-id="0" />

                            </node>

                          </node>

                          <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1069][1032,1323]" drawing-order="3" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1069][734,1323]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/cl_content" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[48,1069][734,1323]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="Bluetooth is turned on now." resource-id="com.transsion.aivoiceassistant:id/robot_text" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1099][686,1152]" drawing-order="1" hint="" display-id="0" />

                                <node NAF="true" index="1" text="" resource-id="com.transsion.aivoiceassistant:id/iv_tts_play" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[614,1206][686,1278]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/check_area" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1353][1032,1704]" drawing-order="4" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_card_layout_ai" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1353][1032,1704]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_content" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1383][1032,1533]" drawing-order="1" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/device_control" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1383][1032,1533]" drawing-order="1" hint="" display-id="0">

                                  <node index="0" text="Bluetooth" resource-id="com.transsion.aivoiceassistant:id/function_name" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1425][834,1486]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/function_control" class="android.widget.Switch" package="com.transsion.aivoiceassistant" content-desc="" checkable="true" checked="true" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[864,1425][984,1491]" drawing-order="2" hint="" display-id="0" />

                                </node>

                              </node>

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1563][1032,1683]" drawing-order="2" hint="" display-id="0">

                                <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_icon" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[96,1596][150,1650]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="Set Up" resource-id="com.transsion.aivoiceassistant:id/tv_title" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[168,1600][286,1646]" drawing-order="2" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="4" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][1032,1830]" drawing-order="5" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][1032,1830]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/bg_like" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1722][339,1830]" drawing-order="1" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_like" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[90,1743][156,1809]" drawing-order="1" hint="" display-id="0" />

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/v_divider" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,1758][195,1794]" drawing-order="2" hint="" display-id="0" />

                                <node NAF="true" index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_unlike" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[231,1743][297,1809]" drawing-order="3" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                          <node index="5" text="" resource-id="" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][1032,1964]" drawing-order="6" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_item_relate_recommend" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][1032,1964]" drawing-order="2" hint="" display-id="0">

                              <node index="0" text="Lower the brightness" resource-id="" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,1860][588,1964]" drawing-order="1" hint="" display-id="0" />

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                    <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/ll_bottom" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="6" hint="" display-id="0">

                      <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/ll_voice_input" class="android.widget.LinearLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="2" hint="" display-id="0">

                        <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input_layout" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="3" hint="" display-id="0">

                          <node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                            <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/rl_root" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1964][1080,2400]" drawing-order="1" hint="" display-id="0">

                              <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/v_bg" class="android.view.View" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][1080,2400]" drawing-order="1" hint="" display-id="0" />

                              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_deep_seek" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,1973][352,2105]" drawing-order="4" hint="" display-id="0">

                                <node index="0" text="DeepSeek-R1" resource-id="com.transsion.aivoiceassistant:id/btn_deep_seek" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[48,2021][352,2105]" drawing-order="1" hint="" display-id="0" />

                              </node>

                              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/iv_input_shadow" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2381]" drawing-order="2" hint="" display-id="0" />

                              <node index="3" text="" resource-id="com.transsion.aivoiceassistant:id/rl_input" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,2105][1080,2340]" drawing-order="5" hint="" display-id="0">

                                <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/lv_ip_anim_view" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="2" hint="" display-id="0">

                                  <node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][192,2276]" drawing-order="1" hint="" display-id="0">

                                    <node index="0" text="" resource-id="com.transsion.aivoiceassistant:id/iv_profile" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[60,2144][181,2265]" drawing-order="1" hint="" display-id="0" />

                                  </node>

                                </node>

                                <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/fl_input" class="android.widget.RelativeLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/et_input" class="android.widget.EditText" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="1" hint="" display-id="0" />

                                  <node index="1" text="Feel free to ask me any questions…" resource-id="com.transsion.aivoiceassistant:id/tv_hint" class="android.widget.TextView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[192,2164][792,2280]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/fl_btn_three_btn" class="android.widget.FrameLayout" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="3" hint="" display-id="0">

                                  <node NAF="true" index="0" text="" resource-id="com.transsion.aivoiceassistant:id/btn_voice" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[792,2174][888,2270]" drawing-order="2" hint="" display-id="0" />

                                </node>

                                <node NAF="true" index="3" text="" resource-id="com.transsion.aivoiceassistant:id/btn_expand" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[912,2174][1008,2270]" drawing-order="4" hint="" display-id="0" />

                              </node>

                            </node>

                          </node>

                        </node>

                      </node>

                    </node>

                  </node>

                </node>

              </node>

              <node index="1" text="" resource-id="com.transsion.aivoiceassistant:id/lbg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[0,0][743,422]" drawing-order="1" hint="" display-id="0" />

              <node index="2" text="" resource-id="com.transsion.aivoiceassistant:id/rlg" class="android.widget.ImageView" package="com.transsion.aivoiceassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[320,0][1080,406]" drawing-order="2" hint="" display-id="0" />

            </node>

          </node>

        </node>

      </node>

    </node>

  </node>

</hierarchy>
