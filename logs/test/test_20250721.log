2025-07-21 15:15:26 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 15:15:26 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 15:15:26 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 15:15:26 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 15:15:32 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 15:15:34 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open bluetooth
2025-07-21 15:15:35 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 15:15:37 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: '好的，为你跳转到设置页面'
2025-07-21 15:15:43 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandConcise\failure_test_open_bluetooth_concise_20250721_151542.png
2025-07-21 15:15:43 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandConcise\failure_test_open_bluetooth_concise_20250721_151542.png
2025-07-21 15:15:43 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 15:15:43 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 15:15:43 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 15:16:11 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 15:16:11 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 15:16:11 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 15:16:12 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 15:16:17 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 15:16:19 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open bluetooth
2025-07-21 15:16:20 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 15:16:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 15:16:26 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:146 | ✅ 状态验证通过: False -> True
2025-07-21 15:16:27 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandConcise\test_completed.png
2025-07-21 15:16:27 | INFO | testcases.test_ella.base_ella_test:simple_command_test:195 | 🎉 open bluetooth 测试完成
2025-07-21 15:16:27 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 15:16:27 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 15:16:27 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 15:19:52 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 15:19:52 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 15:19:52 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 15:19:53 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 15:19:58 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 15:20:08 | INFO | testcases.test_ella.test_open_contact:test_open_contact_concise:34 | ✅ 响应包含Done: Done!
2025-07-21 15:20:08 | INFO | testcases.test_ella.test_open_contact:test_open_contact_concise:38 | ✅ Dalier应用已成功打开
2025-07-21 15:20:08 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-21 15:20:08 | INFO | testcases.test_ella.test_open_contact:test_open_contact_concise:48 | 🎉 open contact 测试完成 - 响应包含Done且Dalier应用已打开
2025-07-21 15:20:08 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 15:20:08 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 15:20:08 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 19:44:23 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 19:44:23 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 19:44:23 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 19:44:23 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 19:44:29 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 19:44:32 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open bluetooth
2025-07-21 19:44:32 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 19:44:47 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 19:44:50 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:146 | ✅ 状态验证通过: False -> True
2025-07-21 19:44:51 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaBluetoothCommandConcise\test_completed.png
2025-07-21 19:44:51 | INFO | testcases.test_ella.base_ella_test:simple_command_test:195 | 🎉 open bluetooth 测试完成
2025-07-21 19:44:51 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 19:44:51 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 19:44:51 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 19:52:24 | INFO | __main__:generate_allure_report:110 | 执行命令: allure generate D:\aigc\app_test\reports/allure-results -o D:\aigc\app_test\reports/allure-report --clean
2025-07-21 20:18:00 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:18:00 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:18:00 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:18:00 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:18:06 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:18:16 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:175 | ✅ 响应包含期望内容: done
2025-07-21 20:18:17 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-21 20:18:17 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:18:17 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:18:17 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:18:44 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:18:44 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:18:44 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:18:45 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:18:50 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:18:59 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:178 | ⚠️ 响应未包含期望内容: done111111
2025-07-21 20:18:59 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_concise_20250721_201859.png
2025-07-21 20:18:59 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_concise_20250721_201859.png
2025-07-21 20:18:59 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:18:59 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:18:59 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:19:42 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:19:42 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:19:42 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:19:42 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:19:48 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:19:56 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:178 | ⚠️ 响应未包含期望内容: done
2025-07-21 20:19:57 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_concise_20250721_201956.png
2025-07-21 20:19:57 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_concise_20250721_201956.png
2025-07-21 20:19:57 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:19:57 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:19:57 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:22:51 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:22:51 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:22:51 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:22:52 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:22:57 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:23:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:174 | 响应: done!
2025-07-21 20:23:08 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:176 | ✅ 响应包含期望内容: done
2025-07-21 20:23:09 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-21 20:23:09 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:23:09 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:23:09 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:28:32 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:28:32 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:28:32 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:28:32 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:28:38 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:28:48 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:174 | 响应: done!
2025-07-21 20:28:48 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:176 | ✅ 响应包含期望内容: done
2025-07-21 20:28:48 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-21 20:28:48 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:28:48 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:28:48 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:30:14 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:30:14 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:30:14 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:30:14 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:30:20 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:30:30 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:174 | 响应: done!
2025-07-21 20:30:30 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:179 | ⚠️ 响应未包含期望内容: done11111
2025-07-21 20:30:30 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_concise_20250721_203030.png
2025-07-21 20:30:30 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open_contact_concise_20250721_203030.png
2025-07-21 20:30:30 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:30:30 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:30:30 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-21 20:55:17 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-21 20:55:17 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-21 20:55:17 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-21 20:55:18 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-21 20:55:23 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-21 20:55:23 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open bluetooth打开应用，状态: False
2025-07-21 20:55:25 | INFO | testcases.test_ella.base_ella_test:_execute_command:110 | ✅ 成功执行命令: open bluetooth
2025-07-21 20:55:26 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:122 | 确保返回到Ella应用以获取响应文本
2025-07-21 20:55:28 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response:133 | AI响应: '<node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.android.systemui" content-desc="Android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />'
2025-07-21 20:55:32 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:146 | ✅ 状态验证通过: False -> True
2025-07-21 20:55:32 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-21 20:55:32 | INFO | testcases.test_ella.base_ella_test:simple_command_test:241 | 🎉 open bluetooth 测试完成
2025-07-21 20:55:32 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:183 | 响应: <node index="0" text="" resource-id="" class="android.widget.imageview" package="com.android.systemui" content-desc="android 设置向导通知：" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" visible-to-user="true" bounds="[166,53][203,90]" drawing-order="1" hint="" display-id="0" />
2025-07-21 20:55:32 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:206 | ⚠️ 响应未包含期望内容: 'done'
2025-07-21 20:55:32 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:213 | ❌ 部分期望内容未找到 (0/1)
2025-07-21 20:55:32 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:214 | 缺失内容: ['done']
2025-07-21 20:55:32 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open__20250721_205532.png
2025-07-21 20:55:32 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\failure_test_open__20250721_205532.png
2025-07-21 20:55:33 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-21 20:55:33 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-21 20:55:33 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
